# キャンペーンマスタバッチ 単体テスト仕様書（物理削除対応）

## ヘッダー情報

| 項目 | 内容 |
|------|------|
| テスト対象ID | IF-CP002-DF01 キャンペーンマスタ |
| テスト対象機能 | 物理削除対応 |
| 作成日 | 2025/06/25 |
| 更新日 | 2025/06/25 |
| バッチID | JN_CP002-DF01_001 |

## 処理概要

本バッチは以下の処理を実行します：
1. OMS DBからクーポン関連データを抽出
2. 内部DBへのUPSERT処理
3. キャンペーン関連データのファイル出力（並列処理）
4. 同期タイムスタンプの更新

## テストケース一覧

### 削除フラグ基本動作検証 (13件)

| No. | テストカテゴリ | サブカテゴリ | テスト内容 | 前提条件 | 手順 | 期待結果 | 優先度 |
|-----|----------------|--------------|------------|----------|------|----------|--------|
| 001 | 削除フラグ基本動作検証 | delete_flg値検証 | delete_flg = 0（未削除）のレコード処理 | OMS DBにdelete_flg = 0のクーポンデータが存在 | バッチ実行 | 正常にファイル出力される | 高 |
| 002 | 削除フラグ基本動作検証 | delete_flg値検証 | delete_flg = 1（論理削除）のレコード処理 | OMS DBにdelete_flg = 1のクーポンデータが存在 | バッチ実行 | 削除フラグ付きでファイル出力される | 高 |
| 003 | 削除フラグ基本動作検証 | delete_flg値検証 | delete_flgがNULLのレコード処理 | OMS DBにdelete_flgがNULLのクーポンデータが存在 | バッチ実行 | エラーまたはデフォルト値で処理される | 中 |
| 004 | 削除フラグ基本動作検証 | 混在データ検証 | 削除・未削除データの混在処理 | delete_flg = 0と1のデータが混在 | バッチ実行 | 各レコードの削除フラグが正しく出力される | 高 |
| 005 | 削除フラグ基本動作検証 | 物理削除確認 | delete_flg = 1のデータが物理削除されない確認 | 論理削除済みデータが存在 | バッチ実行 | 物理削除されずファイル出力される | 高 |
| 006 | 削除フラグ基本動作検証 | タイムスタンプ確認 | 削除データのタイムスタンプ更新確認 | 削除済みデータの更新日時確認 | バッチ実行 | 削除データもタイムスタンプ条件で抽出される | 中 |
| 007 | 削除フラグ基本動作検証 | 出力ファイル確認 | 削除フラグがCSVファイルに正しく出力 | 削除・未削除データ混在 | ファイル内容確認 | delete_flgカラムが正しく出力されている | 高 |
| 008 | 削除フラグ基本動作検証 | UPSERT処理確認 | 削除データのUPSERT処理確認 | 削除済みデータがwork テーブルに存在 | UPSERT実行 | 削除フラグも含めて正しくUPSERTされる | 高 |
| 009 | 削除フラグ基本動作検証 | 並列処理確認 | 並列処理時の削除フラグ整合性確認 | 複数テーブルに削除データ存在 | 並列実行 | 全ての並列処理で削除フラグが正しく処理される | 中 |
| 010 | 削除フラグ基本動作検証 | エラーハンドリング | 削除フラグ不正値のエラーハンドリング | delete_flgに2以上の値が存在 | バッチ実行 | 適切なエラーハンドリングが実行される | 中 |
| 011 | 削除フラグ基本動作検証 | 性能確認 | 大量削除データの処理性能確認 | 10万件の削除データが存在 | バッチ実行 | 制限時間内に処理が完了する | 低 |
| 012 | 削除フラグ基本動作検証 | 復旧処理確認 | 削除から復旧したデータの処理確認 | delete_flg = 1→0に変更されたデータ | バッチ実行 | 復旧データが正しく処理される | 中 |
| 013 | 削除フラグ基本動作検証 | 整合性確認 | 関連テーブル間の削除フラグ整合性確認 | 親子関係テーブルの削除状態確認 | バッチ実行 | 関連テーブル間で削除フラグが整合している | 高 |

### テーブル別UPSERT処理検証 (87件)

#### coupon_view_work関連 (10件)

| No. | テストカテゴリ | サブカテゴリ | テスト内容 | 前提条件 | 手順 | 期待結果 | 優先度 |
|-----|----------------|--------------|------------|----------|------|----------|--------|
| 014 | テーブル別UPSERT処理検証 | coupon_view_work | 新規データのINSERT処理 | work テーブルが空、OMS DBに新規データ | UPSERT実行 | 新規データが正常にINSERTされる | 高 |
| 015 | テーブル別UPSERT処理検証 | coupon_view_work | 既存データのUPDATE処理 | work テーブルに既存データ、OMS DBに更新データ | UPSERT実行 | 既存データが正常にUPDATEされる | 高 |
| 016 | テーブル別UPSERT処理検証 | coupon_view_work | 主キー重複時の処理 | 同一coupon_management_codeのデータ | UPSERT実行 | ON CONFLICTでUPDATEが実行される | 高 |
| 017 | テーブル別UPSERT処理検証 | coupon_view_work | d_versionの増分確認 | 既存データのd_version = 1 | UPDATE実行 | d_versionが2に増分される | 中 |
| 018 | テーブル別UPSERT処理検証 | coupon_view_work | d_updated_datetimeの更新確認 | 既存データが存在 | UPDATE実行 | d_updated_datetimeが現在時刻に更新される | 中 |
| 019 | テーブル別UPSERT処理検証 | coupon_view_work | d_created_userの設定確認 | 新規データ | INSERT実行 | d_created_userが'JN_CP002-DF01_001'に設定される | 中 |
| 020 | テーブル別UPSERT処理検証 | coupon_view_work | delete_flgのUPSERT確認 | 削除フラグ付きデータ | UPSERT実行 | delete_flgが正しくUPSERTされる | 高 |
| 021 | テーブル別UPSERT処理検証 | coupon_view_work | 大量データのUPSERT性能 | 10万件のデータ | 一括UPSERT実行 | 制限時間内に処理が完了する | 低 |
| 022 | テーブル別UPSERT処理検証 | coupon_view_work | NULL値のUPSERT処理 | NULLを含むデータ | UPSERT実行 | NULL値が正しく処理される | 中 |
| 023 | テーブル別UPSERT処理検証 | coupon_view_work | トランザクション整合性確認 | UPSERT途中でエラー発生 | エラー発生 | ロールバックが正しく実行される | 高 |

#### coupon_commodity_view_work関連 (10件)

| No. | テストカテゴリ | サブカテゴリ | テスト内容 | 前提条件 | 手順 | 期待結果 | 優先度 |
|-----|----------------|--------------|------------|----------|------|----------|--------|
| 024 | テーブル別UPSERT処理検証 | coupon_commodity_view_work | 新規データのINSERT処理 | work テーブルが空 | UPSERT実行 | 新規データが正常にINSERTされる | 高 |
| 025 | テーブル別UPSERT処理検証 | coupon_commodity_view_work | 複合キーでのUPSERT処理 | 複合キー（coupon_management_code, shop_code, commodity_code） | UPSERT実行 | 複合キーでの重複制御が正しく動作する | 高 |
| 026 | テーブル別UPSERT処理検証 | coupon_commodity_view_work | 関連データの整合性確認 | 親テーブル（coupon）との関連確認 | UPSERT実行 | 参照整合性が保たれる | 高 |
| 027 | テーブル別UPSERT処理検証 | coupon_commodity_view_work | delete_flgの継承確認 | 削除フラグ付きデータ | UPSERT実行 | delete_flgが正しく継承される | 高 |
| 028 | テーブル別UPSERT処理検証 | coupon_commodity_view_work | タイムスタンプの更新確認 | 既存データの更新 | UPDATE実行 | updated_datetimeが正しく更新される | 中 |
| 029 | テーブル別UPSERT処理検証 | coupon_commodity_view_work | orm_rowidの処理確認 | ORM ROWIDを持つデータ | UPSERT実行 | orm_rowidが正しく処理される | 中 |
| 030 | テーブル別UPSERT処理検証 | coupon_commodity_view_work | 商品コードの検証 | 不正な商品コード | UPSERT実行 | 適切なエラーハンドリングが実行される | 中 |
| 031 | テーブル別UPSERT処理検証 | coupon_commodity_view_work | 店舗コードの検証 | 不正な店舗コード | UPSERT実行 | 適切なエラーハンドリングが実行される | 中 |
| 032 | テーブル別UPSERT処理検証 | coupon_commodity_view_work | 大量データの処理性能 | 50万件のデータ | 一括UPSERT実行 | 制限時間内に処理が完了する | 低 |
| 033 | テーブル別UPSERT処理検証 | coupon_commodity_view_work | 並行実行時の整合性 | 複数プロセスでの同時UPSERT | 並行実行 | データの整合性が保たれる | 中 |

#### campaign_instructions関連 (9件)

| No. | テストカテゴリ | サブカテゴリ | テスト内容 | 前提条件 | 手順 | 期待結果 | 優先度 |
|-----|----------------|--------------|------------|----------|------|----------|--------|
| 034 | テーブル別UPSERT処理検証 | campaign_instructions | キャンペーン指示マスタの新規作成 | 新規キャンペーン指示データ | INSERT実行 | 新規データが正常に作成される | 高 |
| 035 | テーブル別UPSERT処理検証 | campaign_instructions | キャンペーン指示マスタの更新 | 既存キャンペーン指示データ | UPDATE実行 | 既存データが正常に更新される | 高 |
| 036 | テーブル別UPSERT処理検証 | campaign_instructions | キャンペーン優先度の処理 | campaign_priorityを持つデータ | 処理実行 | 優先度が正しく処理される | 中 |
| 037 | テーブル別UPSERT処理検証 | campaign_instructions | キャンペーン適用範囲の処理 | campaign_applied_scopeを持つデータ | 処理実行 | 適用範囲が正しく処理される | 中 |
| 038 | テーブル別UPSERT処理検証 | campaign_instructions | キャンペーン利用制限の処理 | campaign_use_limitを持つデータ | 処理実行 | 利用制限が正しく処理される | 中 |
| 039 | テーブル別UPSERT処理検証 | campaign_instructions | 日付範囲の検証 | campaign_start_date, campaign_end_date | 処理実行 | 日付範囲が正しく処理される | 高 |
| 040 | テーブル別UPSERT処理検証 | campaign_instructions | フラグ項目の処理 | 各種フラグ項目 | 処理実行 | フラグ項目が正しく処理される | 中 |
| 041 | テーブル別UPSERT処理検証 | campaign_instructions | 媒体コードの処理 | baitai_codeを持つデータ | 処理実行 | 媒体コードが正しく処理される | 中 |
| 042 | テーブル別UPSERT処理検証 | campaign_instructions | delete_flgによる論理削除処理 | delete_flg = 1のデータ | 処理実行 | 論理削除が正しく処理される | 高 |

#### campaign_order_view関連 (9件)

| No. | テストカテゴリ | サブカテゴリ | テスト内容 | 前提条件 | 手順 | 期待結果 | 優先度 |
|-----|----------------|--------------|------------|----------|------|----------|--------|
| 043 | テーブル別UPSERT処理検証 | campaign_order_view | キャンペーン注文条件の新規作成 | 新規注文条件データ | INSERT実行 | 新規データが正常に作成される | 高 |
| 044 | テーブル別UPSERT処理検証 | campaign_order_view | 条件タイプの処理 | joken_typeを持つデータ | 処理実行 | 条件タイプが正しく処理される | 中 |
| 045 | テーブル別UPSERT処理検証 | campaign_order_view | 条件番号の処理 | campaign_joken_noを持つデータ | 処理実行 | 条件番号が正しく処理される | 中 |
| 046 | テーブル別UPSERT処理検証 | campaign_order_view | 条件種別の処理 | joken_kind1, joken_kind2 | 処理実行 | 条件種別が正しく処理される | 中 |
| 047 | テーブル別UPSERT処理検証 | campaign_order_view | 条件値の範囲処理 | joken_min, joken_max | 処理実行 | 条件値範囲が正しく処理される | 中 |
| 048 | テーブル別UPSERT処理検証 | campaign_order_view | 定期回数の処理 | regular_kaijiを持つデータ | 処理実行 | 定期回数が正しく処理される | 中 |
| 049 | テーブル別UPSERT処理検証 | campaign_order_view | 月数条件の処理 | joken_month_numを持つデータ | 処理実行 | 月数条件が正しく処理される | 中 |
| 050 | テーブル別UPSERT処理検証 | campaign_order_view | 商品名の処理 | commodity_nameを持つデータ | 処理実行 | 商品名が正しく処理される | 低 |
| 051 | テーブル別UPSERT処理検証 | campaign_order_view | delete_flgによる論理削除処理 | delete_flg = 1のデータ | 処理実行 | 論理削除が正しく処理される | 高 |

#### campaign_order_group_view関連 (8件)

| No. | テストカテゴリ | サブカテゴリ | テスト内容 | 前提条件 | 手順 | 期待結果 | 優先度 |
|-----|----------------|--------------|------------|----------|------|----------|--------|
| 052 | テーブル別UPSERT処理検証 | campaign_order_group_view | キャンペーン注文グループの新規作成 | 新規グループデータ | INSERT実行 | 新規データが正常に作成される | 高 |
| 053 | テーブル別UPSERT処理検証 | campaign_order_group_view | グループ番号の処理 | campaign_group_noを持つデータ | 処理実行 | グループ番号が正しく処理される | 中 |
| 054 | テーブル別UPSERT処理検証 | campaign_order_group_view | 条件表示の処理 | campaign_joken_dispを持つデータ | 処理実行 | 条件表示が正しく処理される | 中 |
| 055 | テーブル別UPSERT処理検証 | campaign_order_group_view | 除外条件表示の処理 | exclude_joken_dispを持つデータ | 処理実行 | 除外条件表示が正しく処理される | 中 |
| 056 | テーブル別UPSERT処理検証 | campaign_order_group_view | グループとオーダーの関連性確認 | 関連するcampaign_orderデータ | 処理実行 | グループとオーダーの関連が正しく保たれる | 高 |
| 057 | テーブル別UPSERT処理検証 | campaign_order_group_view | 複合キーでの重複制御 | 同一指示コード・グループ番号 | INSERT実行 | 重複制御が正しく動作する | 高 |
| 058 | テーブル別UPSERT処理検証 | campaign_order_group_view | delete_flgによる論理削除処理 | delete_flg = 1のデータ | 処理実行 | 論理削除が正しく処理される | 高 |
| 059 | テーブル別UPSERT処理検証 | campaign_order_group_view | タイムスタンプ条件での抽出確認 | d_updated_datetime条件 | SELECT実行 | タイムスタンプ条件で正しく抽出される | 中 |

#### campaign_promotion_view関連 (8件)

| No. | テストカテゴリ | サブカテゴリ | テスト内容 | 前提条件 | 手順 | 期待結果 | 優先度 |
|-----|----------------|--------------|------------|----------|------|----------|--------|
| 060 | テーブル別UPSERT処理検証 | campaign_promotion_view | キャンペーンプロモーションの新規作成 | 新規プロモーションデータ | INSERT実行 | 新規データが正常に作成される | 高 |
| 061 | テーブル別UPSERT処理検証 | campaign_promotion_view | プロモーション情報の更新 | 既存プロモーションデータ | UPDATE実行 | 既存データが正常に更新される | 高 |
| 062 | テーブル別UPSERT処理検証 | campaign_promotion_view | プロモーション条件の処理 | 複雑なプロモーション条件 | 処理実行 | プロモーション条件が正しく処理される | 中 |
| 063 | テーブル別UPSERT処理検証 | campaign_promotion_view | 割引情報の処理 | 割引率・割引額データ | 処理実行 | 割引情報が正しく処理される | 中 |
| 064 | テーブル別UPSERT処理検証 | campaign_promotion_view | 適用期間の処理 | 開始・終了日時データ | 処理実行 | 適用期間が正しく処理される | 中 |
| 065 | テーブル別UPSERT処理検証 | campaign_promotion_view | プロモーション種別の処理 | 各種プロモーション種別 | 処理実行 | プロモーション種別が正しく処理される | 中 |
| 066 | テーブル別UPSERT処理検証 | campaign_promotion_view | delete_flgによる論理削除処理 | delete_flg = 1のデータ | 処理実行 | 論理削除が正しく処理される | 高 |
| 067 | テーブル別UPSERT処理検証 | campaign_promotion_view | タイムスタンプ条件での抽出確認 | d_updated_datetime条件 | SELECT実行 | タイムスタンプ条件で正しく抽出される | 中 |

#### campaign_instructions_commodity関連 (8件)

| No. | テストカテゴリ | サブカテゴリ | テスト内容 | 前提条件 | 手順 | 期待結果 | 優先度 |
|-----|----------------|--------------|------------|----------|------|----------|--------|
| 068 | テーブル別UPSERT処理検証 | campaign_instructions_commodity | キャンペーン指示商品の新規作成 | 新規指示商品データ | INSERT実行 | 新規データが正常に作成される | 高 |
| 069 | テーブル別UPSERT処理検証 | campaign_instructions_commodity | 商品との関連確認 | 商品マスタとの関連 | 処理実行 | 商品との関連が正しく保たれる | 高 |
| 070 | テーブル別UPSERT処理検証 | campaign_instructions_commodity | キャンペーン指示との関連確認 | campaign_instructionsとの関連 | 処理実行 | キャンペーン指示との関連が正しく保たれる | 高 |
| 071 | テーブル別UPSERT処理検証 | campaign_instructions_commodity | 複合キーでの重複制御 | 同一指示コード・商品コード | INSERT実行 | 重複制御が正しく動作する | 高 |
| 072 | テーブル別UPSERT処理検証 | campaign_instructions_commodity | 適用商品の処理 | 複数商品への適用 | 処理実行 | 複数商品への適用が正しく処理される | 中 |
| 073 | テーブル別UPSERT処理検証 | campaign_instructions_commodity | 除外商品の処理 | 除外対象商品 | 処理実行 | 除外商品が正しく処理される | 中 |
| 074 | テーブル別UPSERT処理検証 | campaign_instructions_commodity | delete_flgによる論理削除処理 | delete_flg = 1のデータ | 処理実行 | 論理削除が正しく処理される | 高 |
| 075 | テーブル別UPSERT処理検証 | campaign_instructions_commodity | タイムスタンプ条件での抽出確認 | d_updated_datetime条件 | SELECT実行 | タイムスタンプ条件で正しく抽出される | 中 |

#### campaign_combi_limit関連 (8件)

| No. | テストカテゴリ | サブカテゴリ | テスト内容 | 前提条件 | 手順 | 期待結果 | 優先度 |
|-----|----------------|--------------|------------|----------|------|----------|--------|
| 076 | テーブル別UPSERT処理検証 | campaign_combi_limit | キャンペーン組み合わせ制限の新規作成 | 新規組み合わせ制限データ | INSERT実行 | 新規データが正常に作成される | 高 |
| 077 | テーブル別UPSERT処理検証 | campaign_combi_limit | 組み合わせ制限条件の処理 | 複雑な組み合わせ制限 | 処理実行 | 組み合わせ制限が正しく処理される | 中 |
| 078 | テーブル別UPSERT処理検証 | campaign_combi_limit | 制限対象キャンペーンの処理 | 複数キャンペーンの制限 | 処理実行 | 制限対象が正しく処理される | 中 |
| 079 | テーブル別UPSERT処理検証 | campaign_combi_limit | 制限レベルの処理 | 各種制限レベル | 処理実行 | 制限レベルが正しく処理される | 中 |
| 080 | テーブル別UPSERT処理検証 | campaign_combi_limit | 組み合わせ可否の処理 | 組み合わせ可否判定 | 処理実行 | 組み合わせ可否が正しく処理される | 中 |
| 081 | テーブル別UPSERT処理検証 | campaign_combi_limit | 優先度による制御 | 優先度設定データ | 処理実行 | 優先度による制御が正しく動作する | 中 |
| 082 | テーブル別UPSERT処理検証 | campaign_combi_limit | delete_flgによる論理削除処理 | delete_flg = 1のデータ | 処理実行 | 論理削除が正しく処理される | 高 |
| 083 | テーブル別UPSERT処理検証 | campaign_combi_limit | タイムスタンプ条件での抽出確認 | d_updated_datetime条件 | SELECT実行 | タイムスタンプ条件で正しく抽出される | 中 |

#### coupon_commodity_view関連 (9件)

| No. | テストカテゴリ | サブカテゴリ | テスト内容 | 前提条件 | 手順 | 期待結果 | 優先度 |
|-----|----------------|--------------|------------|----------|------|----------|--------|
| 084 | テーブル別UPSERT処理検証 | coupon_commodity_view | クーポン商品の新規作成 | 新規クーポン商品データ | INSERT実行 | 新規データが正常に作成される | 高 |
| 085 | テーブル別UPSERT処理検証 | coupon_commodity_view | クーポンとの関連確認 | クーポンマスタとの関連 | 処理実行 | クーポンとの関連が正しく保たれる | 高 |
| 086 | テーブル別UPSERT処理検証 | coupon_commodity_view | 商品との関連確認 | 商品マスタとの関連 | 処理実行 | 商品との関連が正しく保たれる | 高 |
| 087 | テーブル別UPSERT処理検証 | coupon_commodity_view | 店舗別商品設定の処理 | 店舗別の商品設定 | 処理実行 | 店舗別設定が正しく処理される | 中 |
| 088 | テーブル別UPSERT処理検証 | coupon_commodity_view | 適用商品の処理 | 複数商品への適用 | 処理実行 | 複数商品への適用が正しく処理される | 中 |
| 089 | テーブル別UPSERT処理検証 | coupon_commodity_view | 除外商品の処理 | 除外対象商品 | 処理実行 | 除外商品が正しく処理される | 中 |
| 090 | テーブル別UPSERT処理検証 | coupon_commodity_view | 複合キーでの重複制御 | 同一クーポン・店舗・商品 | INSERT実行 | 重複制御が正しく動作する | 高 |
| 091 | テーブル別UPSERT処理検証 | coupon_commodity_view | delete_flgによる論理削除処理 | delete_flg = 1のデータ | 処理実行 | 論理削除が正しく処理される | 高 |
| 092 | テーブル別UPSERT処理検証 | coupon_commodity_view | タイムスタンプ条件での抽出確認 | d_updated_datetime条件 | SELECT実行 | タイムスタンプ条件で正しく抽出される | 中 |

#### set_commodity_composition_view関連 (8件)

| No. | テストカテゴリ | サブカテゴリ | テスト内容 | 前提条件 | 手順 | 期待結果 | 優先度 |
|-----|----------------|--------------|------------|----------|------|----------|--------|
| 093 | テーブル別UPSERT処理検証 | set_commodity_composition_view | セット商品構成の新規作成 | 新規セット商品構成データ | INSERT実行 | 新規データが正常に作成される | 高 |
| 094 | テーブル別UPSERT処理検証 | set_commodity_composition_view | 親商品との関連確認 | 親商品コードとの関連 | 処理実行 | 親商品との関連が正しく保たれる | 高 |
| 095 | テーブル別UPSERT処理検証 | set_commodity_composition_view | 子商品との関連確認 | 子商品コードとの関連 | 処理実行 | 子商品との関連が正しく保たれる | 高 |
| 096 | テーブル別UPSERT処理検証 | set_commodity_composition_view | 構成数量の処理 | composition_quantityの処理 | 処理実行 | 構成数量が正しく処理される | 中 |
| 097 | テーブル別UPSERT処理検証 | set_commodity_composition_view | 構成順序の処理 | composition_orderの処理 | 処理実行 | 構成順序が正しく処理される | 中 |
| 098 | テーブル別UPSERT処理検証 | set_commodity_composition_view | 複合キーでの重複制御 | 同一店舗・親商品・子商品 | INSERT実行 | 重複制御が正しく動作する | 高 |
| 099 | テーブル別UPSERT処理検証 | set_commodity_composition_view | delete_flgによる論理削除処理 | delete_flg = 1のデータ | 処理実行 | 論理削除が正しく処理される | 高 |
| 100 | テーブル別UPSERT処理検証 | set_commodity_composition_view | タイムスタンプ条件での抽出確認 | d_updated_datetime条件 | SELECT実行 | タイムスタンプ条件で正しく抽出される | 中 |

### Step Functions制御検証 (8件)

| No. | テストカテゴリ | サブカテゴリ | テスト内容 | 前提条件 | 手順 | 期待結果 | 優先度 |
|-----|----------------|--------------|------------|----------|------|----------|--------|
| 101 | Step Functions制御検証 | 実行制御 | 重複実行の防止確認 | Step Functionsが既に実行中 | 新規実行開始 | ExecutionAlreadyRunningエラーで停止する | 高 |
| 102 | Step Functions制御検証 | 並列処理制御 | 並列処理（parallel_5_to_13）の正常動作 | 複数テーブルのデータが存在 | 並列処理実行 | 全ての並列処理が正常に完了する | 高 |
| 103 | Step Functions制御検証 | エラーハンドリング | 並列処理中の1つのジョブが失敗 | 並列処理中にエラー発生 | エラー発生 | Step Functionsが失敗状態となる | 高 |
| 104 | Step Functions制御検証 | タイムスタンプ生成 | 日時情報の正確な生成 | Step Functions開始 | 日時生成処理 | full_date, short_dateが正しく生成される | 中 |
| 105 | Step Functions制御検証 | JSONata処理 | JSONata式の正常動作確認 | 動的パラメータを含むジョブ | ジョブ実行 | JSONata式が正しく評価される | 中 |
| 106 | Step Functions制御検証 | ログ出力 | CloudWatch Logsへの出力確認 | Step Functions実行 | ログ確認 | 実行ログが正しく出力される | 低 |
| 107 | Step Functions制御検証 | 同期タイムスタンプ更新 | 最終SQL実行（sql_common_update_003）の確認 | 全処理完了後 | 最終SQL実行 | 同期タイムスタンプが正しく更新される | 高 |
| 108 | Step Functions制御検証 | リトライ制御 | Glueジョブ失敗時のリトライ動作 | Glueジョブでエラー発生 | リトライ実行 | 設定されたリトライ回数まで再実行される | 中 |

## テスト実行時の注意事項

### 前提条件
- 各テストは独立して実行可能であること
- テストデータは事前に準備済みであること
- OMS DB、内部DBの接続が正常であること

### 優先度別実行順序
1. **高優先度（68件）**: 基本機能・削除フラグ処理・主要UPSERT処理
2. **中優先度（35件）**: 詳細検証・エラーハンドリング・性能関連
3. **低優先度（5件）**: 性能測定・ログ確認・補助機能

### 期待される成果物
- 各テストケースの実行結果レポート
- 不具合検出時の詳細調査レポート
- 性能測定結果（該当テストケース）
- 推奨改善事項リスト

---

*テスト仕様書バージョン: 1.0*
*最終更新日: 2025/06/25*
