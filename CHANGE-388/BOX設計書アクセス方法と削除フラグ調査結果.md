# BOX設計書アクセス方法と削除フラグ調査結果

## 📁 BOX設計書へのアクセス方法

### 1. WSL環境でのBOX Driveマウント

正式設計書はWindows側のBOX Driveにありますが、WSL環境からもアクセス可能です。

#### マウント手順
```bash
# マウントポイント作成
sudo mkdir -p /mnt/box

# BOX Driveをマウント
sudo mount -t drvfs 'C:\Users\<USER>\Box' /mnt/box

# アクセス確認
ls /mnt/box
```

#### 参考資料
- [WSL2 support for Box Drives](https://support.box.com/hc/en-us/community/posts/4407787058835-WSL2-support-for-Box-Drives)
- 解決策提供者: Frank Goytisolo

### 2. マウント後のパス構造

| Windows側パス | WSL側パス |
|---------------|-----------|
| `C:\Users\<USER>\Box` | `/mnt/box` |
| `C:\Users\<USER>\Box\1760_DHC_MDM_連携基盤_パートナー共有` | `/mnt/box/1760_DHC_MDM_連携基盤_パートナー共有` |

## 📋 関連設計書一覧

### 主要設計書の場所

#### 1. ジョブスケジュール設計書
```
Windows側: C:\Users\<USER>\Box\1760_DHC_MDM_連携基盤_パートナー共有\030.内部設計\020.データ連携基盤\01.Sharepoint→BOX管理になった設計書\020.ジョブスケジュール\ジョブスケジュール_連携基盤.xlsx

WSL側: /mnt/box/1760_DHC_MDM_連携基盤_パートナー共有/030.内部設計/020.データ連携基盤/01.Sharepoint→BOX管理になった設計書/020.ジョブスケジュール/ジョブスケジュール_連携基盤.xlsx
```

#### 2. テーブル設計書
```
Windows側: C:\Users\<USER>\Box\1760_DHC_MDM_連携基盤_パートナー共有\030.内部設計\020.データ連携基盤\01.Sharepoint→BOX管理になった設計書\050.テーブル設計書\テーブル設計書_連携基盤.xlsm

WSL側: /mnt/box/1760_DHC_MDM_連携基盤_パートナー共有/030.内部設計/020.データ連携基盤/01.Sharepoint→BOX管理になった設計書/050.テーブル設計書/テーブル設計書_連携基盤.xlsm
```

#### 3. SQL定義書
```
Windows側: C:\Users\<USER>\Box\1760_DHC_MDM_連携基盤_パートナー共有\030.内部設計\020.データ連携基盤\01.Sharepoint→BOX管理になった設計書\080.SQL定義書\SQL定義書_連携基盤.xlsx

WSL側: /mnt/box/1760_DHC_MDM_連携基盤_パートナー共有/030.内部設計/020.データ連携基盤/01.Sharepoint→BOX管理になった設計書/080.SQL定義書/SQL定義書_連携基盤.xlsx
```

#### 4. SQL定義書（DWH関連仕様変更版）
```
Windows側: C:\Users\<USER>\Box\1760_DHC_MDM_連携基盤_パートナー共有\030.内部設計\020.データ連携基盤\01.Sharepoint→BOX管理になった設計書\080.SQL定義書\SQL定義書_連携基盤_仕様変更_DWH関連.xlsx

WSL側: /mnt/box/1760_DHC_MDM_連携基盤_パートナー共有/030.内部設計/020.データ連携基盤/01.Sharepoint→BOX管理になった設計書/080.SQL定義書/SQL定義書_連携基盤_仕様変更_DWH関連.xlsx
```

## 🔍 IF-CP002-DF01 削除フラグ調査結果

### 調査対象
- **インターフェース**: IF-CP002-DF01 キャンペーンマスタ
- **対象SQL**: sql_CP002-DF01_select_001 ～ sql_CP002-DF01_select_011 (11個)

### 調査結果サマリー

| 調査対象 | 削除フラグ実装済み | 削除フラグ不足 | 合計 |
|----------|-------------------|----------------|------|
| 現在のプロジェクトSQLファイル | 1個 | 10個 | 11個 |
| BOXのSQL定義書 | 1個 | 10個 | 11個 |

### 詳細調査結果

#### ✅ 削除フラグ実装済み (1個)
| SQL ID | テーブル名 | 削除フラグカラム | 備考 |
|--------|------------|------------------|------|
| sql_CP002-DF01_select_003 | campaign_instructions | delete_flg | 実装済み |

#### ❌ 削除フラグ不足 (10個)
| SQL ID | テーブル名 | 現在の状況 | 必要な対応 |
|--------|------------|------------|------------|
| sql_CP002-DF01_select_001 | coupon | 削除フラグなし | delete_flg追加 |
| sql_CP002-DF01_select_002 | coupon_commodity | 削除フラグなし | delete_flg追加 |
| sql_CP002-DF01_select_004 | coupon | 削除フラグなし | delete_flg追加 |
| sql_CP002-DF01_select_005 | campaign_order | 削除フラグなし | delete_flg追加 |
| sql_CP002-DF01_select_006 | campaign_order_group | 削除フラグなし | delete_flg追加 |
| sql_CP002-DF01_select_007 | campaign_promotion | 削除フラグなし | delete_flg追加 |
| sql_CP002-DF01_select_008 | campaign_instructions_commodity | 削除フラグなし | delete_flg追加 |
| sql_CP002-DF01_select_009 | campaign_combi_limit | 削除フラグなし | delete_flg追加 |
| sql_CP002-DF01_select_010 | coupon_commodity | 削除フラグなし | delete_flg追加 |
| sql_CP002-DF01_select_011 | set_commodity_composition | 削除フラグなし | delete_flg追加 |

## 📊 影響範囲調査書との対応関係

### OMS物理削除(ファイル出力影響)での分類
- **対応グループ**: 削除F追加
- **連携先**: POS、DWH
- **対応方法**: 論理削除フラグを追加して連携

### 対象テーブルと英語名対応
| 日本語テーブル名 | 英語テーブル名 | 対応状況 |
|------------------|----------------|----------|
| キャンペーン設定 | campaign_instructions | ✅ 実装済み |
| クーポン | coupon | ❌ 要対応 |
| クーポン適用商品 | coupon_commodity | ❌ 要対応 |
| キャンペーン設定条件 | campaign_order | ❌ 要対応 |
| キャンペーン設定条件グループ | campaign_order_group | ❌ 要対応 |
| キャンペーン設定プロモーション | campaign_promotion | ❌ 要対応 |
| キャンペーン設定商品 | campaign_instructions_commodity | ❌ 要対応 |
| キャンペーン併用不可 | campaign_combi_limit | ❌ 要対応 |
| セット商品構成 | set_commodity_composition | ❌ 要対応 |

## 🔧 必要な修正作業

### 1. プロジェクト内SQLファイルの修正
- **場所**: `job/source/sql/sql_CP002-DF01_select_*.sql`
- **修正内容**: 各SQLのSELECT文に `delete_flg` カラムを追加

### 2. BOX設計書の更新
- **場所**: BOXのSQL定義書
- **修正内容**: 各シートの取得カラム一覧に `delete_flg` を追加

### 3. Step Functions設定の確認
- **場所**: `cloudformation/templates/step-functions/dlpf_JN_CP002-DF01_001.yaml`
- **確認内容**: SQLファイル変更に伴う影響確認

## 📝 技術的な実装詳細

### 削除フラグの仕様
- **カラム名**: `delete_flg`
- **データ型**: 通常 `CHAR(1)` または `INTEGER`
- **値**: 0=有効, 1=削除 (一般的な仕様)

### 実装パターン（参考: sql_CP002-DF01_select_003）
```sql
SELECT campaign_instructions_code,
    campaign_instructions_name,
    delete_flg,  -- ← この行が追加されている
    campaign_priority,
    -- ... 他のカラム
FROM campaign_instructions
WHERE d_updated_datetime > :sync_datetime
    AND d_updated_datetime <= :diff_base_timestamp;
```

## 🚨 重要な発見事項

1. **BOX設計書も更新が必要**: 現在のプロジェクトだけでなく、BOXの正式設計書も同様に削除フラグが不足
2. **影響範囲の広さ**: CP002だけで10個のSQLが影響を受ける
3. **下流システムへの影響**: POS、DWHシステムへの連携で削除フラグが必要

## 📅 調査実施日
- **調査日**: 2025年6月16日
- **調査者**: Augment Agent
- **調査方法**: BOX設計書とプロジェクト内SQLファイルの比較分析
