-- DLPF DB 追加テストデータ投入（空ファイル対策）
-- 目的: CSVファイル出力検証で0 bytesファイルを改善するため
-- 1. campaign_order_view テストデータ
INSERT INTO dlpf.campaign_order_view (
        campaign_instructions_code,
        campaign_group_no,
        joken_type,
        campaign_joken_no,
        joken_kind1,
        joken_kind2,
        joken,
        joken_min,
        joken_max,
        regular_kaiji,
        joken_month_num,
        commodity_name,
        orm_rowid,
        created_user,
        created_datetime,
        updated_user,
        updated_datetime,
        delete_flg,
        d_created_user,
        d_created_datetime,
        d_updated_user,
        d_updated_datetime,
        d_version
    )
VALUES (
        'TEST_CAMP_001',
        1,
        '1',
        1,
        '1',
        '001',
        'TEST_JOKEN_001',
        1000,
        5000,
        12,
        3,
        'テスト商品001',
        2001,
        'test_user',
        NOW(),
        'test_user',
        NOW(),
        0,
        'test_user',
        NOW(),
        'test_user',
        NOW(),
        1
    ),
    (
        'TEST_CAMP_001',
        1,
        '2',
        2,
        '2',
        '002',
        'TEST_JOKEN_002',
        2000,
        10000,
        6,
        1,
        'テスト商品002',
        2002,
        'test_user',
        NOW(),
        'test_user',
        NOW(),
        1,
        'test_user',
        NOW(),
        'test_user',
        NOW(),
        1
    ),
    (
        'TEST_CAMP_002',
        2,
        '1',
        3,
        '1',
        '003',
        'TEST_JOKEN_003',
        500,
        3000,
        24,
        6,
        'テスト商品003',
        2003,
        'test_user',
        NOW(),
        'test_user',
        NOW(),
        0,
        'test_user',
        NOW(),
        'test_user',
        NOW(),
        1
    );
-- 2. campaign_order_group_view テストデータ
INSERT INTO dlpf.campaign_order_group_view (
        campaign_instructions_code,
        campaign_group_no,
        campaign_joken_disp,
        exclude_joken_disp,
        orm_rowid,
        created_user,
        created_datetime,
        updated_user,
        updated_datetime,
        delete_flg,
        d_created_user,
        d_created_datetime,
        d_updated_user,
        d_updated_datetime,
        d_version
    )
VALUES (
        'TEST_CAMP_001',
        1,
        'テスト条件表示001',
        '除外条件001',
        3001,
        'test_user',
        NOW(),
        'test_user',
        NOW(),
        0,
        'test_user',
        NOW(),
        'test_user',
        NOW(),
        1
    ),
    (
        'TEST_CAMP_001',
        2,
        'テスト条件表示002',
        '除外条件002',
        3002,
        'test_user',
        NOW(),
        'test_user',
        NOW(),
        1,
        'test_user',
        NOW(),
        'test_user',
        NOW(),
        1
    ),
    (
        'TEST_CAMP_002',
        1,
        'テスト条件表示003',
        '',
        3003,
        'test_user',
        NOW(),
        'test_user',
        NOW(),
        0,
        'test_user',
        NOW(),
        'test_user',
        NOW(),
        1
    );
-- 3. campaign_promotion_view テストデータ
INSERT INTO dlpf.campaign_promotion_view (
        campaign_instructions_code,
        promotion_no,
        promotion_type,
        shop_code,
        commodity_code,
        commodity_name,
        present_qt,
        discount_rate,
        discount_amount,
        discount_retail_price,
        shipping_charge,
        orm_rowid,
        created_user,
        created_datetime,
        updated_user,
        updated_datetime,
        delete_flg,
        d_created_user,
        d_created_datetime,
        d_updated_user,
        d_updated_datetime,
        d_version
    )
VALUES (
        'TEST_CAMP_001',
        1,
        '01',
        'SHOP001',
        'COMMODITY_001',
        'テスト商品001',
        1,
        10,
        100,
        1000,
        0,
        4001,
        'test_user',
        NOW(),
        'test_user',
        NOW(),
        0,
        'test_user',
        NOW(),
        'test_user',
        NOW(),
        1
    ),
    (
        'TEST_CAMP_001',
        2,
        '02',
        'SHOP001',
        'COMMODITY_002',
        'テスト商品002',
        2,
        20,
        200,
        2000,
        500,
        4002,
        'test_user',
        NOW(),
        'test_user',
        NOW(),
        1,
        'test_user',
        NOW(),
        'test_user',
        NOW(),
        1
    ),
    (
        'TEST_CAMP_002',
        1,
        '03',
        'SHOP002',
        'COMMODITY_003',
        'テスト商品003',
        1,
        5,
        50,
        500,
        0,
        4003,
        'test_user',
        NOW(),
        'test_user',
        NOW(),
        0,
        'test_user',
        NOW(),
        'test_user',
        NOW(),
        1
    );
-- 4. campaign_instructions_commodity_view テストデータ
INSERT INTO dlpf.campaign_instructions_commodity_view (
        campaign_instructions_code,
        shop_code,
        commodity_code,
        joken_type,
        orm_rowid,
        created_user,
        created_datetime,
        updated_user,
        updated_datetime,
        delete_flg,
        d_created_user,
        d_created_datetime,
        d_updated_user,
        d_updated_datetime,
        d_version
    )
VALUES (
        'TEST_CAMP_001',
        'SHOP001',
        'COMMODITY_001',
        '1',
        5001,
        'test_user',
        NOW(),
        'test_user',
        NOW(),
        0,
        'test_user',
        NOW(),
        'test_user',
        NOW(),
        1
    ),
    (
        'TEST_CAMP_001',
        'SHOP001',
        'COMMODITY_002',
        '2',
        5002,
        'test_user',
        NOW(),
        'test_user',
        NOW(),
        1,
        'test_user',
        NOW(),
        'test_user',
        NOW(),
        1
    ),
    (
        'TEST_CAMP_002',
        'SHOP002',
        'COMMODITY_003',
        '1',
        5003,
        'test_user',
        NOW(),
        'test_user',
        NOW(),
        0,
        'test_user',
        NOW(),
        'test_user',
        NOW(),
        1
    );
-- 5. campaign_combi_limit_view テストデータ
INSERT INTO dlpf.campaign_combi_limit_view (
        campaign_instructions_code,
        campaign_combi_limit_code,
        orm_rowid,
        created_user,
        created_datetime,
        updated_user,
        updated_datetime,
        delete_flg,
        d_created_user,
        d_created_datetime,
        d_updated_user,
        d_updated_datetime,
        d_version
    )
VALUES (
        'TEST_CAMP_001',
        'LIMIT_001',
        6001,
        'test_user',
        NOW(),
        'test_user',
        NOW(),
        0,
        'test_user',
        NOW(),
        'test_user',
        NOW(),
        1
    ),
    (
        'TEST_CAMP_001',
        'LIMIT_002',
        6002,
        'test_user',
        NOW(),
        'test_user',
        NOW(),
        1,
        'test_user',
        NOW(),
        'test_user',
        NOW(),
        1
    ),
    (
        'TEST_CAMP_002',
        'LIMIT_003',
        6003,
        'test_user',
        NOW(),
        'test_user',
        NOW(),
        0,
        'test_user',
        NOW(),
        'test_user',
        NOW(),
        1
    );
-- データ投入確認用クエリ
SELECT 'campaign_order_view' as table_name,
    COUNT(*) as record_count
FROM dlpf.campaign_order_view
WHERE campaign_instructions_code LIKE 'TEST_CAMP%'
UNION ALL
SELECT 'campaign_order_group_view',
    COUNT(*)
FROM dlpf.campaign_order_group_view
WHERE campaign_instructions_code LIKE 'TEST_CAMP%'
UNION ALL
SELECT 'campaign_promotion_view',
    COUNT(*)
FROM dlpf.campaign_promotion_view
WHERE campaign_instructions_code LIKE 'TEST_CAMP%'
UNION ALL
SELECT 'campaign_instructions_commodity_view',
    COUNT(*)
FROM dlpf.campaign_instructions_commodity_view
WHERE campaign_instructions_code LIKE 'TEST_CAMP%'
UNION ALL
SELECT 'campaign_combi_limit_view',
    COUNT(*)
FROM dlpf.campaign_combi_limit_view
WHERE campaign_instructions_code LIKE 'TEST_CAMP%';