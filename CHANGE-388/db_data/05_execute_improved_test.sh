#!/bin/bash
# CP002-DF01 改善後テスト実行スクリプト
# 目的: DLPF DBテストデータ投入 + Step Functions実行

set -e

echo "=== CP002-DF01 改善後テスト実行開始 ==="

# 1. DLPF DB追加テストデータ投入
echo "1. DLPF DBに追加テストデータを投入中..."
PGPASSWORD=password psql -h localhost -p 15432 -U dlpf_ope -d dlpf-dev -f CHANGE-388/db_data/04_dlpf_db_additional_test_data.sql

if [ $? -eq 0 ]; then
    echo "✅ DLPF DBテストデータ投入完了"
else
    echo "❌ DLPF DBテストデータ投入失敗"
    exit 1
fi

# 2. Step Functions実行
echo "2. Step Functions実行中..."
EXECUTION_NAME="test-execution-improved-$(date +%Y%m%d-%H%M%S)"
echo "実行名: $EXECUTION_NAME"

EXECUTION_ARN=$(aws stepfunctions start-execution \
    --state-machine-arn "arn:aws:states:ap-northeast-1:886436956581:stateMachine:JN_CP002-DF01_001" \
    --name "$EXECUTION_NAME" \
    --input '{}' \
    --query 'executionArn' \
    --output text)

echo "実行ARN: $EXECUTION_ARN"

# 3. 実行完了待機
echo "3. 実行完了を待機中..."
while true; do
    STATUS=$(aws stepfunctions describe-execution \
        --execution-arn "$EXECUTION_ARN" \
        --query 'status' \
        --output text)
    
    echo "$(date '+%H:%M:%S') - Status: $STATUS"
    
    if [ "$STATUS" != "RUNNING" ]; then
        echo "実行完了: $STATUS"
        break
    fi
    
    sleep 30
done

# 4. 結果確認
if [ "$STATUS" = "SUCCEEDED" ]; then
    echo "✅ Step Functions実行成功"
    
    # S3出力ファイル確認
    echo "4. S3出力ファイル確認中..."
    EXECUTION_DATE=$(date +%Y%m%d)
    aws s3 ls s3://s3-dev-dlpf-if-886436956581/input-output/SQG_OUT/ --recursive | \
        grep "$EXECUTION_DATE" | grep "JN_CP002-DF01_001" | tail -10
    
    echo "=== テスト実行完了 ==="
    echo "実行ARN: $EXECUTION_ARN"
    echo "実行名: $EXECUTION_NAME"
else
    echo "❌ Step Functions実行失敗: $STATUS"
    exit 1
fi
