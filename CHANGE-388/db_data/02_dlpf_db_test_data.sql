-- =====================================================
-- DLPF DB テストデータ投入SQL
-- IF-CP002-DF01 単体テスト用
-- =====================================================
-- 実行対象: DLPF DB (DLPF_DB_INFO)
-- 作成日: 2025/06/26
-- 対応テスト仕様書: unit_test_spec_JN_CP002-DF01_001_ver1.1.md

-- =====================================================
-- 1. sync_timestamp テーブル初期化
-- =====================================================
-- テスト用のタイムスタンプ設定

-- 既存のCP002関連データクリア
DELETE FROM dlpf.sync_timestamp WHERE job_schedule_id = 'JN_CP002-DF01_001';

-- 初期タイムスタンプ設定（差分抽出用）
INSERT INTO dlpf.sync_timestamp (
    job_schedule_id,
    file_name,
    sync_datetime,
    d_created_user,
    d_created_datetime,
    d_updated_user,
    d_updated_datetime,
    d_version
) VALUES 
-- 各ファイルの同期タイムスタンプを過去に設定（テストデータが抽出されるように）
('JN_CP002-DF01_001', 'coupon_oms', '2024-12-31 23:59:59', 'test_user', NOW(), 'test_user', NOW(), 1),
('JN_CP002-DF01_001', 'coupon_commodity_oms', '2024-12-31 23:59:59', 'test_user', NOW(), 'test_user', NOW(), 1),
('JN_CP002-DF01_001', 'campaign_order', '2024-12-31 23:59:59', 'test_user', NOW(), 'test_user', NOW(), 1),
('JN_CP002-DF01_001', 'campaign_order_group', '2024-12-31 23:59:59', 'test_user', NOW(), 'test_user', NOW(), 1),
('JN_CP002-DF01_001', 'campaign_promotion', '2024-12-31 23:59:59', 'test_user', NOW(), 'test_user', NOW(), 1),
('JN_CP002-DF01_001', 'campaign_instructions_commodity', '2024-12-31 23:59:59', 'test_user', NOW(), 'test_user', NOW(), 1),
('JN_CP002-DF01_001', 'campaign_combi_limit', '2024-12-31 23:59:59', 'test_user', NOW(), 'test_user', NOW(), 1),
('JN_CP002-DF01_001', 'set_commodity_composition', '2024-12-31 23:59:59', 'test_user', NOW(), 'test_user', NOW(), 1),
('JN_CP002-DF01_001', 'coupon', '2024-12-31 23:59:59', 'test_user', NOW(), 'test_user', NOW(), 1),
('JN_CP002-DF01_001', 'coupon_commodity', '2024-12-31 23:59:59', 'test_user', NOW(), 'test_user', NOW(), 1),
('JN_CP002-DF01_001', 'campaign_instructions', '2024-12-31 23:59:59', 'test_user', NOW(), 'test_user', NOW(), 1);

-- =====================================================
-- 2. workテーブルクリア
-- =====================================================
-- テストケース014, 015: UPSERT処理検証用

-- 全workテーブルをクリア
TRUNCATE TABLE dlpf.coupon_view_work;
TRUNCATE TABLE dlpf.coupon_commodity_view_work;
TRUNCATE TABLE dlpf.campaign_order_view_work;
TRUNCATE TABLE dlpf.campaign_order_group_view_work;
TRUNCATE TABLE dlpf.campaign_promotion_view_work;
TRUNCATE TABLE dlpf.campaign_instructions_commodity_view_work;
TRUNCATE TABLE dlpf.campaign_combi_limit_view_work;
TRUNCATE TABLE dlpf.set_commodity_composition_view_work;

-- =====================================================
-- 3. 既存データ準備（UPDATE検証用）
-- =====================================================
-- テストケース015: workテーブル既存、OMS DB更新データ

-- coupon_view_work に既存データを投入（UPDATE検証用）
INSERT INTO dlpf.coupon_view_work (
    coupon_management_code,
    coupon_code,
    coupon_name,
    coupon_invalid_flag,
    coupon_type,
    coupon_issue_type,
    coupon_use_limit,
    coupon_use_purchase_price,
    coupon_discount_type,
    coupon_discount_rate,
    coupon_discount_price,
    coupon_start_datetime,
    coupon_end_datetime,
    coupon_limit_display_period,
    coupon_limit_display,
    coupon_description,
    coupon_message,
    coupon_kbn,
    coupon_post_in_charge,
    coupon_commodity_flag,
    marketing_channel_list,
    goods_group,
    commodity_category_code,
    commodity_series,
    orm_rowid,
    created_user,
    created_datetime,
    updated_user,
    updated_datetime,
    delete_flg
) VALUES 
-- 既存データ（後でUPDATEされる予定）
('TEST002', 'COUPON002_OLD', 'テストクーポン002（更新前）', 0, 1, 1, 1, 1500, 1, 5, NULL, 
 '2024-01-01 00:00:00', '2024-12-31 23:59:59', 10, '10日前まで表示', 
 '更新前の説明', '更新前のメッセージ', '01', 'OLD_DEPT', 0, 
 'EC', 'OLD_GROUP', 'OLD_CAT', 'OLD01', 999, 'old_user', '2024-01-01 00:00:00', 'old_user', '2024-01-01 00:00:00', 0);

-- coupon_commodity_view_work に既存データを投入
INSERT INTO dlpf.coupon_commodity_view_work (
    coupon_management_code,
    shop_code,
    commodity_code,
    orm_rowid,
    created_user,
    created_datetime,
    updated_user,
    updated_datetime,
    delete_flg
) VALUES 
('TEST002', 'OLD_SHOP', 'OLD_COMM', 999, 'old_user', '2024-01-01 00:00:00', 'old_user', '2024-01-01 00:00:00', 0);

-- =====================================================
-- 4. 最終テーブル既存データ準備
-- =====================================================
-- UPSERT処理で最終テーブルに反映されるデータの準備

-- 既存の最終テーブルデータをクリア
DELETE FROM dlpf.coupon_view WHERE coupon_management_code LIKE 'TEST%';
DELETE FROM dlpf.coupon_commodity_view WHERE coupon_management_code LIKE 'TEST%';
DELETE FROM dlpf.campaign_order_view WHERE campaign_instructions_code LIKE 'TEST%';
DELETE FROM dlpf.campaign_order_group_view WHERE campaign_instructions_code LIKE 'TEST%';
DELETE FROM dlpf.campaign_promotion_view WHERE campaign_instructions_code LIKE 'TEST%';
DELETE FROM dlpf.campaign_instructions_commodity_view WHERE campaign_instructions_code LIKE 'TEST%';
DELETE FROM dlpf.campaign_combi_limit_view WHERE campaign_instructions_code LIKE 'TEST%';
DELETE FROM dlpf.set_commodity_composition_view WHERE set_commodity_code LIKE 'TEST%';

-- 一部の最終テーブルに既存データを投入（UPSERT UPDATE検証用）
INSERT INTO dlpf.coupon_view (
    coupon_management_code,
    coupon_code,
    coupon_name,
    coupon_invalid_flag,
    coupon_type,
    coupon_issue_type,
    coupon_use_limit,
    coupon_use_purchase_price,
    coupon_discount_type,
    coupon_discount_rate,
    coupon_discount_price,
    coupon_start_datetime,
    coupon_end_datetime,
    coupon_limit_display_period,
    coupon_limit_display,
    coupon_description,
    coupon_message,
    coupon_kbn,
    coupon_post_in_charge,
    coupon_commodity_flag,
    marketing_channel_list,
    goods_group,
    commodity_category_code,
    commodity_series,
    orm_rowid,
    created_user,
    created_datetime,
    updated_user,
    updated_datetime,
    d_created_user,
    d_created_datetime,
    d_updated_user,
    d_updated_datetime,
    d_version,
    delete_flg
) VALUES 
-- 既存データ（UPSERTでUPDATEされる予定）
('TEST002', 'COUPON002_FINAL_OLD', 'テストクーポン002（最終テーブル更新前）', 0, 1, 1, 1, 1200, 1, 3, NULL, 
 '2024-01-01 00:00:00', '2024-12-31 23:59:59', 5, '5日前まで表示', 
 '最終テーブル更新前の説明', '最終テーブル更新前のメッセージ', '01', 'FINAL_OLD_DEPT', 0, 
 'EC', 'FINAL_OLD_GROUP', 'FINAL_OLD_CAT', 'FIN01', 888, 'final_old_user', '2024-01-01 00:00:00', 'final_old_user', '2024-01-01 00:00:00',
 'JN_CP002-DF01_001', '2024-01-01 00:00:00', 'JN_CP002-DF01_001', '2024-01-01 00:00:00', 1, 0);

-- =====================================================
-- データ投入完了確認
-- =====================================================
\echo 'DLPF DB テストデータ投入完了'
\echo '投入件数確認:'
SELECT 'sync_timestamp' as table_name, COUNT(*) as count FROM dlpf.sync_timestamp WHERE job_schedule_id = 'JN_CP002-DF01_001'
UNION ALL
SELECT 'coupon_view_work', COUNT(*) FROM dlpf.coupon_view_work WHERE coupon_management_code LIKE 'TEST%'
UNION ALL
SELECT 'coupon_commodity_view_work', COUNT(*) FROM dlpf.coupon_commodity_view_work WHERE coupon_management_code LIKE 'TEST%'
UNION ALL
SELECT 'coupon_view (final)', COUNT(*) FROM dlpf.coupon_view WHERE coupon_management_code LIKE 'TEST%';
