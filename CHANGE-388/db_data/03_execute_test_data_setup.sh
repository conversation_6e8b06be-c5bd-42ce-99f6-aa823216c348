#!/bin/bash

# =====================================================
# IF-CP002-DF01 テストデータ投入実行スクリプト
# =====================================================
# 作成日: 2025/06/26
# 対応テスト仕様書: unit_test_spec_JN_CP002-DF01_001_ver1.1.md

set -e  # エラー時に停止

# カラー出力用
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== IF-CP002-DF01 テストデータ投入開始 ===${NC}"

# =====================================================
# 設定値
# =====================================================
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
OMS_SQL_FILE="${SCRIPT_DIR}/01_oms_db_test_data.sql"
DLPF_SQL_FILE="${SCRIPT_DIR}/02_dlpf_db_test_data.sql"

# データベース接続情報
DLPF_HOST="localhost"
DLPF_PORT="15432"
DLPF_USER="dlpf_ope"        # DLPF DB用ユーザー（デフォルトスキーマ: dlpf）
DLPF_PASSWORD="password"
DLPF_DB="dlpf-dev"

# =====================================================
# 関数定義
# =====================================================
check_file_exists() {
    local file=$1
    if [ ! -f "$file" ]; then
        echo -e "${RED}エラー: ファイルが見つかりません: $file${NC}"
        exit 1
    fi
}

get_oms_db_info() {
    echo -e "${YELLOW}OMS DB接続情報を設定中...${NC}"

    # 固定値で設定（ポートフォワード環境用）
    OMS_HOST="localhost"      # ポートフォワード先
    OMS_PORT="15432"          # ポートフォワード先ポート
    OMS_USER="webshop"        # OMS DB用ユーザー
    OMS_PASSWORD="password"   # OMS DB用パスワード
    OMS_DB="dlpf-dev"         # データベース名

    echo -e "${GREEN}✅ OMS DB接続情報設定完了${NC}"
    echo -e "${BLUE}  Host: $OMS_HOST:$OMS_PORT (ポートフォワード)${NC}"
    echo -e "${BLUE}  Database: $OMS_DB${NC}"
    echo -e "${BLUE}  User: $OMS_USER (デフォルトスキーマ: oms_readreplica)${NC}"
}

execute_sql() {
    local host=$1
    local port=$2
    local user=$3
    local password=$4
    local database=$5
    local sql_file=$6
    local description=$7

    echo -e "${YELLOW}$description を実行中...${NC}"

    export PGPASSWORD="$password"

    if psql -h "$host" -p "$port" -U "$user" -d "$database" -f "$sql_file"; then
        echo -e "${GREEN}✅ $description 完了${NC}"
    else
        echo -e "${RED}❌ $description 失敗${NC}"
        unset PGPASSWORD
        exit 1
    fi

    unset PGPASSWORD
}

# =====================================================
# 前提条件チェック
# =====================================================
echo -e "${YELLOW}前提条件をチェック中...${NC}"

# SQLファイルの存在確認
check_file_exists "$OMS_SQL_FILE"
check_file_exists "$DLPF_SQL_FILE"

# psqlコマンドの存在確認
if ! command -v psql &> /dev/null; then
    echo -e "${RED}エラー: psqlコマンドが見つかりません${NC}"
    exit 1
fi

# jqコマンドの存在確認
if ! command -v jq &> /dev/null; then
    echo -e "${RED}エラー: jqコマンドが見つかりません${NC}"
    echo -e "${YELLOW}jqをインストールしてください: sudo apt-get install jq${NC}"
    exit 1
fi

echo -e "${GREEN}✅ 前提条件チェック完了${NC}"

# =====================================================
# データベース接続確認
# =====================================================
echo -e "${YELLOW}データベース接続確認中...${NC}"

# DLPF DB接続確認
export PGPASSWORD="$DLPF_PASSWORD"
if psql -h "$DLPF_HOST" -p "$DLPF_PORT" -U "$DLPF_USER" -d "$DLPF_DB" -c "SELECT 1;" > /dev/null 2>&1; then
    echo -e "${GREEN}✅ DLPF DB接続確認完了${NC}"
else
    echo -e "${RED}❌ DLPF DB接続失敗${NC}"
    unset PGPASSWORD
    exit 1
fi
unset PGPASSWORD

# OMS DB接続情報取得
get_oms_db_info

# OMS DB接続確認
export PGPASSWORD="$OMS_PASSWORD"
if psql -h "$OMS_HOST" -p "$OMS_PORT" -U "$OMS_USER" -d "$OMS_DB" -c "SELECT 1;" > /dev/null 2>&1; then
    echo -e "${GREEN}✅ OMS DB接続確認完了${NC}"
    OMS_DB_AVAILABLE=true
else
    echo -e "${YELLOW}⚠️ OMS DB接続失敗（スキップして続行）${NC}"
    OMS_DB_AVAILABLE=false
fi
unset PGPASSWORD

# =====================================================
# 実行確認
# =====================================================
echo -e "${YELLOW}以下のデータベースにテストデータを投入します:${NC}"
echo "  DLPF DB: $DLPF_HOST:$DLPF_PORT/$DLPF_DB"
if [ "$OMS_DB_AVAILABLE" = true ]; then
    echo "  OMS DB:  $OMS_HOST:$OMS_PORT/$OMS_DB ✅"
else
    echo "  OMS DB:  接続不可（スキップ） ⚠️"
fi
echo ""
read -p "続行しますか？ (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo -e "${YELLOW}処理を中止しました${NC}"
    exit 0
fi

# =====================================================
# テストデータ投入実行
# =====================================================

# 1. DLPF DB テストデータ投入
execute_sql "$DLPF_HOST" "$DLPF_PORT" "$DLPF_USER" "$DLPF_PASSWORD" "$DLPF_DB" "$DLPF_SQL_FILE" "DLPF DB テストデータ投入"

# 2. OMS DB テストデータ投入
if [ "$OMS_DB_AVAILABLE" = true ]; then
    execute_sql "$OMS_HOST" "$OMS_PORT" "$OMS_USER" "$OMS_PASSWORD" "$OMS_DB" "$OMS_SQL_FILE" "OMS DB テストデータ投入"
else
    echo -e "${YELLOW}OMS DB テストデータ投入をスキップしました（接続不可）${NC}"
    echo -e "${YELLOW}OMS DBへの投入は以下のコマンドで手動実行してください:${NC}"
    echo "  PGPASSWORD=\"\$OMS_PASSWORD\" psql -h \"\$OMS_HOST\" -p \"\$OMS_PORT\" -U \"\$OMS_USER\" -d \"\$OMS_DB\" -f $OMS_SQL_FILE"
fi

# =====================================================
# 完了
# =====================================================
echo ""
echo -e "${GREEN}=== テストデータ投入完了 ===${NC}"
echo -e "${BLUE}次のステップ:${NC}"
if [ "$OMS_DB_AVAILABLE" = true ]; then
    echo "1. ✅ OMS DB テストデータ投入完了"
    echo "2. IF-CP002-DF01 Step Functionsを実行"
    echo "3. 単体テスト仕様書に従ってテストを実施"
else
    echo "1. ⚠️ OMS DBに手動でテストデータを投入"
    echo "2. IF-CP002-DF01 Step Functionsを実行"
    echo "3. 単体テスト仕様書に従ってテストを実施"
fi
echo ""
echo -e "${YELLOW}テストデータ確認コマンド:${NC}"
echo "  # DLPF DB確認"
echo "  PGPASSWORD=$DLPF_PASSWORD psql -h $DLPF_HOST -p $DLPF_PORT -U $DLPF_USER -d $DLPF_DB"
echo "  SELECT COUNT(*) FROM dlpf.sync_timestamp WHERE job_schedule_id = 'JN_CP002-DF01_001';"
if [ "$OMS_DB_AVAILABLE" = true ]; then
    echo ""
    echo "  # OMS DB確認"
    echo "  PGPASSWORD=\"$OMS_PASSWORD\" psql -h \"$OMS_HOST\" -p \"$OMS_PORT\" -U \"$OMS_USER\" -d \"$OMS_DB\""
    echo "  SELECT COUNT(*) FROM coupon_view WHERE coupon_management_code LIKE 'TEST%';"
fi
