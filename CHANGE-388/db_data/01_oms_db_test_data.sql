-- =====================================================
-- OMS DB テストデータ投入SQL
-- IF-CP002-DF01 単体テスト用
-- =====================================================
-- 実行対象: OMS DB (OMS_DB_INFO)
-- 作成日: 2025/06/26
-- 対応テスト仕様書: unit_test_spec_JN_CP002-DF01_001_ver1.1.md
-- =====================================================
-- 1. coupon_view テストデータ
-- =====================================================
-- テストケース001-006: 削除フラグ基本動作検証用
-- 既存データクリア
DELETE FROM oms_readreplica.coupon_view
WHERE coupon_management_code LIKE 'TEST%';
-- delete_flg = 0 のテストデータ（有効データ）
INSERT INTO oms_readreplica.coupon_view (
        coupon_management_code,
        coupon_code,
        coupon_name,
        coupon_invalid_flag,
        coupon_type,
        coupon_issue_type,
        coupon_use_limit,
        coupon_use_purchase_price,
        coupon_discount_type,
        coupon_discount_rate,
        coupon_discount_price,
        coupon_start_datetime,
        coupon_end_datetime,
        coupon_limit_display_period,
        coupon_limit_display,
        coupon_description,
        coupon_message,
        coupon_kbn,
        coupon_post_in_charge,
        coupon_commodity_flag,
        marketing_channel_list,
        goods_group,
        commodity_category_code,
        commodity_series,
        orm_rowid,
        created_user,
        created_datetime,
        updated_user,
        updated_datetime,
        delete_flg
    )
VALUES -- テストケース001: delete_flg = 0のレコード
    (
        'TEST001',
        'COUPON001',
        'テストクーポン001（有効）',
        0,
        1,
        1,
        1,
        1000,
        1,
        10,
        NULL,
        '2025-01-01 00:00:00',
        '2025-12-31 23:59:59',
        30,
        '30日前まで表示',
        'テスト用クーポン説明001',
        'テスト用メッセージ001',
        '01',
        'TEST_DEPT',
        0,
        'EC,POS',
        'GROUP001',
        'CAT001',
        'SER01',
        1001,
        'test_user',
        NOW(),
        'test_user',
        NOW(),
        0
    ),
    (
        'TEST002',
        'COUPON002',
        'テストクーポン002（有効）',
        0,
        1,
        1,
        1,
        2000,
        2,
        NULL,
        500,
        '2025-01-01 00:00:00',
        '2025-12-31 23:59:59',
        15,
        '15日前まで表示',
        'テスト用クーポン説明002',
        'テスト用メッセージ002',
        '02',
        'TEST_DEPT',
        1,
        'EC',
        'GROUP002',
        'CAT002',
        'SER02',
        1002,
        'test_user',
        NOW(),
        'test_user',
        NOW(),
        0
    ),
    -- テストケース002: delete_flg = 1のレコード（論理削除）
    (
        'TEST003',
        'COUPON003',
        'テストクーポン003（削除済み）',
        0,
        1,
        1,
        1,
        3000,
        1,
        15,
        NULL,
        '2025-01-01 00:00:00',
        '2025-12-31 23:59:59',
        7,
        '7日前まで表示',
        'テスト用クーポン説明003',
        'テスト用メッセージ003',
        '03',
        'TEST_DEPT',
        0,
        'POS',
        'GROUP003',
        'CAT003',
        'SER03',
        1003,
        'test_user',
        NOW(),
        'test_user',
        NOW(),
        1
    ),
    (
        'TEST004',
        'COUPON004',
        'テストクーポン004（削除済み）',
        0,
        1,
        1,
        1,
        4000,
        2,
        NULL,
        1000,
        '2025-01-01 00:00:00',
        '2025-12-31 23:59:59',
        NULL,
        NULL,
        'テスト用クーポン説明004',
        'テスト用メッセージ004',
        '04',
        'TEST_DEPT',
        1,
        'EC,POS',
        'GROUP004',
        'CAT004',
        'SER04',
        1004,
        'test_user',
        NOW(),
        'test_user',
        NOW(),
        1
    ),
    -- テストケース004: 混在データ検証用（追加）
    (
        'TEST005',
        'COUPON005',
        'テストクーポン005（有効・混在用）',
        0,
        2,
        2,
        2,
        5000,
        1,
        20,
        NULL,
        '2025-01-01 00:00:00',
        '2025-12-31 23:59:59',
        60,
        '60日前まで表示',
        'テスト用クーポン説明005',
        'テスト用メッセージ005',
        '05',
        'TEST_DEPT',
        0,
        'EC',
        'GROUP005',
        'CAT005',
        'SER05',
        1005,
        'test_user',
        NOW(),
        'test_user',
        NOW(),
        0
    ),
    (
        'TEST006',
        'COUPON006',
        'テストクーポン006（削除・混在用）',
        0,
        2,
        2,
        2,
        6000,
        2,
        NULL,
        2000,
        '2025-01-01 00:00:00',
        '2025-12-31 23:59:59',
        90,
        '90日前まで表示',
        'テスト用クーポン説明006',
        'テスト用メッセージ006',
        '06',
        'TEST_DEPT',
        1,
        'POS',
        'GROUP006',
        'CAT006',
        'SER06',
        1006,
        'test_user',
        NOW(),
        'test_user',
        NOW(),
        1
    );
-- =====================================================
-- 2. coupon_commodity_view テストデータ
-- =====================================================
-- テストケース027, 091: coupon_commodity_view delete_flg継承・論理削除処理用
-- 既存データクリア
DELETE FROM oms_readreplica.coupon_commodity_view
WHERE coupon_management_code LIKE 'TEST%';
INSERT INTO oms_readreplica.coupon_commodity_view (
        coupon_management_code,
        shop_code,
        commodity_code,
        orm_rowid,
        created_user,
        created_datetime,
        updated_user,
        updated_datetime,
        delete_flg
    )
VALUES -- 有効データ
    (
        'TEST001',
        'SHOP001',
        'COMM001',
        2001,
        'test_user',
        NOW(),
        'test_user',
        NOW(),
        0
    ),
    (
        'TEST002',
        'SHOP001',
        'COMM002',
        2002,
        'test_user',
        NOW(),
        'test_user',
        NOW(),
        0
    ),
    (
        'TEST005',
        'SHOP002',
        'COMM005',
        2005,
        'test_user',
        NOW(),
        'test_user',
        NOW(),
        0
    ),
    -- 削除データ
    (
        'TEST003',
        'SHOP001',
        'COMM003',
        2003,
        'test_user',
        NOW(),
        'test_user',
        NOW(),
        1
    ),
    (
        'TEST004',
        'SHOP002',
        'COMM004',
        2004,
        'test_user',
        NOW(),
        'test_user',
        NOW(),
        1
    ),
    (
        'TEST006',
        'SHOP002',
        'COMM006',
        2006,
        'test_user',
        NOW(),
        'test_user',
        NOW(),
        1
    );
-- =====================================================
-- 3. campaign_order_view テストデータ
-- =====================================================
-- テストケース051: campaign_order_view delete_flg=1 論理削除処理用
-- 既存データクリア
DELETE FROM campaign_order_view
WHERE campaign_instructions_code LIKE 'TEST%';
INSERT INTO campaign_order_view (
        campaign_instructions_code,
        campaign_group_no,
        joken_type,
        campaign_joken_no,
        joken_kind1,
        joken_kind2,
        joken,
        joken_min,
        joken_max,
        regular_kaiji,
        joken_month_num,
        commodity_name,
        orm_rowid,
        created_user,
        created_datetime,
        updated_user,
        updated_datetime,
        delete_flg
    )
VALUES -- 有効データ
    (
        'TESTCAMP001',
        1,
        '1',
        1,
        '1',
        '101',
        'COND001',
        1000,
        9999,
        NULL,
        12,
        '商品名001',
        3001,
        'test_user',
        NOW(),
        'test_user',
        NOW(),
        0
    ),
    (
        'TESTCAMP002',
        1,
        '1',
        1,
        '2',
        '201',
        'COND002',
        2000,
        8888,
        NULL,
        6,
        '商品名002',
        3002,
        'test_user',
        NOW(),
        'test_user',
        NOW(),
        0
    ),
    -- 削除データ
    (
        'TESTCAMP003',
        1,
        '1',
        1,
        '1',
        '101',
        'COND003',
        3000,
        7777,
        NULL,
        3,
        '商品名003',
        3003,
        'test_user',
        NOW(),
        'test_user',
        NOW(),
        1
    ),
    (
        'TESTCAMP004',
        2,
        '2',
        1,
        '2',
        '202',
        'COND004',
        4000,
        6666,
        NULL,
        1,
        '商品名004',
        3004,
        'test_user',
        NOW(),
        'test_user',
        NOW(),
        1
    );
-- =====================================================
-- 4. campaign_order_group_view テストデータ
-- =====================================================
-- テストケース058: campaign_order_group_view delete_flg=1 論理削除処理用
-- 既存データクリア
DELETE FROM campaign_order_group_view
WHERE campaign_instructions_code LIKE 'TEST%';
INSERT INTO campaign_order_group_view (
        campaign_instructions_code,
        campaign_group_no,
        campaign_group_name,
        campaign_group_type,
        orm_rowid,
        created_user,
        created_datetime,
        updated_user,
        updated_datetime,
        delete_flg
    )
VALUES -- 有効データ
    (
        'TESTCAMP001',
        1,
        'テストグループ001',
        '1',
        4001,
        'test_user',
        NOW(),
        'test_user',
        NOW(),
        0
    ),
    (
        'TESTCAMP002',
        1,
        'テストグループ002',
        '2',
        4002,
        'test_user',
        NOW(),
        'test_user',
        NOW(),
        0
    ),
    -- 削除データ
    (
        'TESTCAMP003',
        1,
        'テストグループ003',
        '1',
        4003,
        'test_user',
        NOW(),
        'test_user',
        NOW(),
        1
    ),
    (
        'TESTCAMP004',
        2,
        'テストグループ004',
        '2',
        4004,
        'test_user',
        NOW(),
        'test_user',
        NOW(),
        1
    );
-- =====================================================
-- 5. campaign_promotion_view テストデータ
-- =====================================================
-- テストケース066: campaign_promotion_view delete_flg=1 論理削除処理用
-- 既存データクリア
DELETE FROM campaign_promotion_view
WHERE campaign_instructions_code LIKE 'TEST%';
INSERT INTO campaign_promotion_view (
        campaign_instructions_code,
        promotion_no,
        promotion_type,
        commodity_code,
        discount_rate,
        discount_retail_price,
        orm_rowid,
        created_user,
        created_datetime,
        updated_user,
        updated_datetime,
        delete_flg
    )
VALUES -- 有効データ
    (
        'TESTCAMP001',
        1,
        '01',
        'COMM001',
        10,
        NULL,
        5001,
        'test_user',
        NOW(),
        'test_user',
        NOW(),
        0
    ),
    (
        'TESTCAMP002',
        1,
        '02',
        'COMM002',
        NULL,
        1000,
        5002,
        'test_user',
        NOW(),
        'test_user',
        NOW(),
        0
    ),
    -- 削除データ
    (
        'TESTCAMP003',
        1,
        '01',
        'COMM003',
        15,
        NULL,
        5003,
        'test_user',
        NOW(),
        'test_user',
        NOW(),
        1
    ),
    (
        'TESTCAMP004',
        2,
        '03',
        'COMM004',
        NULL,
        2000,
        5004,
        'test_user',
        NOW(),
        'test_user',
        NOW(),
        1
    );
-- =====================================================
-- 6. campaign_instructions_commodity_view テストデータ
-- =====================================================
-- テストケース074: campaign_instructions_commodity delete_flg=1 論理削除処理用
-- 既存データクリア
DELETE FROM campaign_instructions_commodity_view
WHERE campaign_instructions_code LIKE 'TEST%';
INSERT INTO campaign_instructions_commodity_view (
        campaign_instructions_code,
        commodity_code,
        orm_rowid,
        created_user,
        created_datetime,
        updated_user,
        updated_datetime,
        delete_flg
    )
VALUES -- 有効データ
    (
        'TESTCAMP001',
        'COMM001',
        6001,
        'test_user',
        NOW(),
        'test_user',
        NOW(),
        0
    ),
    (
        'TESTCAMP002',
        'COMM002',
        6002,
        'test_user',
        NOW(),
        'test_user',
        NOW(),
        0
    ),
    -- 削除データ
    (
        'TESTCAMP003',
        'COMM003',
        6003,
        'test_user',
        NOW(),
        'test_user',
        NOW(),
        1
    ),
    (
        'TESTCAMP004',
        'COMM004',
        6004,
        'test_user',
        NOW(),
        'test_user',
        NOW(),
        1
    );
-- =====================================================
-- 7. campaign_combi_limit_view テストデータ
-- =====================================================
-- テストケース082: campaign_combi_limit delete_flg=1 論理削除処理用
-- 既存データクリア
DELETE FROM campaign_combi_limit_view
WHERE campaign_instructions_code LIKE 'TEST%';
INSERT INTO campaign_combi_limit_view (
        campaign_instructions_code,
        campaign_instructions_code_limit,
        orm_rowid,
        created_user,
        created_datetime,
        updated_user,
        updated_datetime,
        delete_flg
    )
VALUES -- 有効データ
    (
        'TESTCAMP001',
        'TESTCAMP002',
        7001,
        'test_user',
        NOW(),
        'test_user',
        NOW(),
        0
    ),
    (
        'TESTCAMP002',
        'TESTCAMP001',
        7002,
        'test_user',
        NOW(),
        'test_user',
        NOW(),
        0
    ),
    -- 削除データ
    (
        'TESTCAMP003',
        'TESTCAMP004',
        7003,
        'test_user',
        NOW(),
        'test_user',
        NOW(),
        1
    ),
    (
        'TESTCAMP004',
        'TESTCAMP003',
        7004,
        'test_user',
        NOW(),
        'test_user',
        NOW(),
        1
    );
-- =====================================================
-- 8. set_commodity_composition_view テストデータ
-- =====================================================
-- テストケース099: set_commodity_composition_view delete_flg=1 論理削除処理用
-- 既存データクリア
DELETE FROM set_commodity_composition_view
WHERE set_commodity_code LIKE 'TEST%';
INSERT INTO set_commodity_composition_view (
        set_commodity_code,
        commodity_code,
        quantity,
        orm_rowid,
        created_user,
        created_datetime,
        updated_user,
        updated_datetime,
        delete_flg
    )
VALUES -- 有効データ
    (
        'TESTSET001',
        'COMM001',
        2,
        8001,
        'test_user',
        NOW(),
        'test_user',
        NOW(),
        0
    ),
    (
        'TESTSET002',
        'COMM002',
        3,
        8002,
        'test_user',
        NOW(),
        'test_user',
        NOW(),
        0
    ),
    -- 削除データ
    (
        'TESTSET003',
        'COMM003',
        1,
        8003,
        'test_user',
        NOW(),
        'test_user',
        NOW(),
        1
    ),
    (
        'TESTSET004',
        'COMM004',
        4,
        8004,
        'test_user',
        NOW(),
        'test_user',
        NOW(),
        1
    );
-- =====================================================
-- データ投入完了確認
-- =====================================================
\ echo 'OMS DB テストデータ投入完了' \ echo '投入件数確認:'
SELECT 'coupon_view' as table_name,
    COUNT(*) as count
FROM coupon_view
WHERE coupon_management_code LIKE 'TEST%'
UNION ALL
SELECT 'coupon_commodity_view',
    COUNT(*)
FROM coupon_commodity_view
WHERE coupon_management_code LIKE 'TEST%'
UNION ALL
SELECT 'campaign_order_view',
    COUNT(*)
FROM campaign_order_view
WHERE campaign_instructions_code LIKE 'TEST%'
UNION ALL
SELECT 'campaign_order_group_view',
    COUNT(*)
FROM campaign_order_group_view
WHERE campaign_instructions_code LIKE 'TEST%'
UNION ALL
SELECT 'campaign_promotion_view',
    COUNT(*)
FROM campaign_promotion_view
WHERE campaign_instructions_code LIKE 'TEST%'
UNION ALL
SELECT 'campaign_instructions_commodity_view',
    COUNT(*)
FROM campaign_instructions_commodity_view
WHERE campaign_instructions_code LIKE 'TEST%'
UNION ALL
SELECT 'campaign_combi_limit_view',
    COUNT(*)
FROM campaign_combi_limit_view
WHERE campaign_instructions_code LIKE 'TEST%'
UNION ALL
SELECT 'set_commodity_composition_view',
    COUNT(*)
FROM set_commodity_composition_view
WHERE set_commodity_code LIKE 'TEST%';