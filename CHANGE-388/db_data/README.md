# IF-CP002-DF01 単体テストデータ投入

## 📋 概要

IF-CP002-DF01 キャンペーンマスタバッチの単体テスト用データベーステストデータ投入SQLとスクリプトです。

**対応テスト仕様書**: `sharepoin_docs/unit_test_spec_JN_CP002-DF01_001_ver1.1.md`

## 📁 ファイル構成

```
CHANGE-388/db_data/
├── README.md                      # このファイル
├── 01_oms_db_test_data.sql        # OMS DB テストデータ投入SQL
├── 02_dlpf_db_test_data.sql       # DLPF DB テストデータ投入SQL
└── 03_execute_test_data_setup.sh  # テストデータ投入実行スクリプト
```

## 🎯 テストケース対応

### 削除フラグ基本動作検証 (テストケース001-006)

| テストケース | データ内容 | 投入先テーブル | delete_flg |
|-------------|-----------|---------------|------------|
| 001 | 有効データ | coupon_view | 0 |
| 002 | 削除データ | coupon_view | 1 |
| 004 | 混在データ | 全テーブル | 0/1混在 |

### UPSERT処理検証 (テストケース014-099)

| テストケース | 対象テーブル | 検証内容 |
|-------------|-------------|----------|
| 014 | coupon_view_work | 新規INSERT |
| 015 | coupon_view_work | UPDATE |
| 020 | 全テーブル | delete_flg UPSERT |
| 027-099 | 各_viewテーブル | 論理削除処理 |

## 📊 投入データ詳細

### OMS DB テストデータ

#### coupon_view (6件)
- `TEST001-002`: delete_flg = 0 (有効データ)
- `TEST003-004`: delete_flg = 1 (削除データ)
- `TEST005-006`: delete_flg = 0/1 (混在検証用)

#### その他テーブル
- `coupon_commodity_view`: 6件 (delete_flg 0/1混在)
- `campaign_order_view`: 4件 (delete_flg 0/1混在)
- `campaign_order_group_view`: 4件 (delete_flg 0/1混在)
- `campaign_promotion_view`: 4件 (delete_flg 0/1混在)
- `campaign_instructions_commodity_view`: 4件 (delete_flg 0/1混在)
- `campaign_combi_limit_view`: 4件 (delete_flg 0/1混在)
- `set_commodity_composition_view`: 4件 (delete_flg 0/1混在)

### DLPF DB テストデータ

#### sync_timestamp (11件)
- 全ファイルの同期タイムスタンプを過去日時に設定
- テストデータが差分抽出されるように調整

#### workテーブル
- 全workテーブルをクリア
- UPDATE検証用に一部既存データを投入

#### 最終テーブル
- UPSERT UPDATE検証用に一部既存データを投入

## 🚀 実行手順

### 1. 自動実行（推奨）

```bash
cd /home/<USER>/works/tis-dlpf-app/CHANGE-388/db_data
./03_execute_test_data_setup.sh
```

### 2. 手動実行

#### DLPF DB への投入
```bash
PGPASSWORD=password psql -h localhost -p 15432 -U dlpf_ope -d dlpf-dev -f 02_dlpf_db_test_data.sql
```

#### OMS DB への投入（要設定）
```bash
# OMS DB接続情報を実際の環境に合わせて設定
PGPASSWORD=$OMS_PASSWORD psql -h $OMS_HOST -p $OMS_PORT -U $OMS_USER -d $OMS_DB -f 01_oms_db_test_data.sql
```

## 🔍 データ確認方法

### 投入データ件数確認
```sql
-- DLPF DB
SELECT 'sync_timestamp' as table_name, COUNT(*) as count 
FROM dlpf.sync_timestamp 
WHERE job_schedule_id = 'JN_CP002-DF01_001';

-- OMS DB (実際の環境で実行)
SELECT 'coupon_view' as table_name, COUNT(*) as count 
FROM coupon_view 
WHERE coupon_management_code LIKE 'TEST%';
```

### delete_flg分布確認
```sql
-- OMS DB
SELECT 
    delete_flg,
    COUNT(*) as count
FROM coupon_view 
WHERE coupon_management_code LIKE 'TEST%'
GROUP BY delete_flg
ORDER BY delete_flg;
```

## 🧪 テスト実行

### Step Functions実行
1. AWS コンソールで `JN_CP002-DF01_001` を実行
2. 実行結果を確認

### 期待結果
- **削除フラグ基本動作**: delete_flg=0/1のデータが正しく出力される
- **UPSERT処理**: workテーブル→最終テーブルへの正常な反映
- **ファイル出力**: delete_flgカラムを含むCSVファイルが生成される

## ⚠️ 注意事項

### OMS DB設定
- OMS DB接続情報は実際の環境に合わせて設定が必要
- `01_oms_db_test_data.sql` の実行前に接続情報を確認

### データクリア
- テストデータは `TEST%` プレフィックスで識別
- 実行前に既存のテストデータをクリア

### 権限
- 各データベースへの適切な権限が必要
- INSERT/DELETE/UPDATE権限を確認

## 🔧 トラブルシューティング

### 接続エラー
```bash
# データベース接続確認
PGPASSWORD=password psql -h localhost -p 15432 -U dlpf_ope -d dlpf-dev -c "SELECT 1;"
```

### テーブル存在確認
```sql
-- テーブル存在確認
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'dlpf' 
  AND table_name LIKE '%view%'
ORDER BY table_name;
```

### データクリア（必要時）
```sql
-- テストデータクリア
DELETE FROM coupon_view WHERE coupon_management_code LIKE 'TEST%';
DELETE FROM dlpf.sync_timestamp WHERE job_schedule_id = 'JN_CP002-DF01_001';
```

## 📞 サポート

問題が発生した場合は、以下を確認してください：
1. データベース接続情報
2. テーブル構造（delete_flgカラムの存在）
3. 権限設定
4. テスト仕様書との対応関係

---

**作成日**: 2025/06/26  
**対応バージョン**: unit_test_spec_JN_CP002-DF01_001_ver1.1
